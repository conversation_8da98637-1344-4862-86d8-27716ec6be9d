<template>
	<view class="container">
		<!-- 页面顶部标题 -->
		<!-- 动态活动列表 -->
		<view class="activity-list">
			<view v-for="(activity, index) in activityList" :key="index" class="activity-item">
				<!-- 活动图片 -->
				<image :src="activity.image" class="activity-image" />
				<!-- 活动详情 -->
				<view class="activity-info">
					<text class="activity-title">本次活动开放时间</text>
					<text class="activity-time">：{{activity.remark}}</text>
					<button :class="activity.status==1? 'active-btn' : 'inactive-btn'" :disabled="activity.status!=1"
						@click="onActivityClick(activity)">
						{{ activity.status==1 ? '进入活动' : '活动已结束' }}
					</button>
				</view>
				<!-- 活动结束水印 -->
				<view v-if="activity.isActive == false" class="activity-ended-watermark">
					活动已结束
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				activityList: [], // 活动数据列表
			};
		},
		onShow() {
			this.loadActivityData();
			this.getapi()
			// this.getapi_2()
		},
		methods: {
			getapi() {
				this.$api.request({
					url: this.$api.getActlist
				}).then((res) => {
					this.activityList = res.rows
				})
			},
			// getapi_2() {
			// 	this.$api.request({
			// 		url: this.$api.changeStatus
			// 	}).then((res) => {
			// 		console.log(res);
			// 	})
			// },
			// 加载活动数据（模拟从后端获取）
			loadActivityData() {
				const currentTime = new Date(); // 当前时间
				this.activityList = [];

				// 检查活动状态
				this.activityList = this.activityList.map((activity) => {
					const startTime = this.parseTime(activity.startTime);
					const endTime = this.parseTime(activity.endTime);
					activity.isActive = currentTime >= startTime && currentTime <= endTime;
					return activity;
				});
			},

			// 解析时间
			parseTime(timeStr) {
				const [hours, minutes, seconds] = timeStr.split(':').map(Number);
				const time = new Date();
				time.setHours(hours, minutes, seconds, 0);
				return time;
			},

			// 刷新页面
			refreshPage() {
				this.loadActivityData();
				uni.showToast({
					title: '活动列表已刷新',
					icon: 'none'
				});
			},

			// 点击活动
			onActivityClick(activity) {
				if (activity.status == 1) {
					uni.navigateTo({
						url: `/pages/seckill/index?id=${activity.id}`,
					});
				} else {
					uni.showToast({
						title: '活动已结束',
						icon: 'none'
					});
				}
			},
		},
	};
</script>

<style scoped>
	.container {
		display: flex;
		flex-direction: column;
		padding: 10rpx;
	}

	.refresh-btn {
		background-color: white;
		color: #3cb371;
		padding: 5rpx 10rpx;
		border-radius: 5rpx;
	}

	.activity-list {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		margin-top: 20rpx;
	}

	.activity-item {
		position: relative;
		background-color: white;
		border-radius: 30rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
	}

	.activity-image {
		width: 100%;
		height: 300rpx;
	}

	.activity-info {
		padding: 10rpx;
	}

	.activity-title {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 5rpx;
	}

	.activity-time {
		font-size: 24rpx;
		color: #666;
		border-radius: 30rpx;
		overflow: hidden;
	}

	.active-btn {
		background-color: #3cb371;
		color: white;
		padding: 5rpx 10rpx;
		border-radius: 5rpx;
		font-size: 24rpx;
		margin-top: 10rpx;
	}

	.inactive-btn {
		background-color: #ccc;
		color: white;
		padding: 5rpx 10rpx;
		border-radius: 5rpx;
		font-size: 24rpx;
		margin-top: 10rpx;
	}

	.activity-ended-watermark {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.6);
		color: white;
		font-size: 36rpx;
		font-style: italic;
		display: flex;
		justify-content: center;
		align-items: center;
		transform: rotate(-20deg);
	}
</style>