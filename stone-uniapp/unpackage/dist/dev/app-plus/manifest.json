{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__F985C22", "name": "玉智慧", "version": {"name": "3.1.23", "code": 1}, "description": "基于uni-app开发的新闻/资讯类App模板", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "light", "background": "#5C3317"}, "usingComponents": true, "nvueCompiler": "uni-app", "distribute": {"icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}, "google": {"permissions": ["<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "permissionPhoneState": {"request": "none"}, "abiFilters": ["armeabi-v7a"]}, "apple": {"idfa": false, "dSYMs": false}, "plugins": {"ad": {}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "uniStatistics": {"enable": false}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#fff", "bottom": {"offset": "auto"}}, "uni-app": {"compilerVersion": "4.36", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "tabBar": {"color": "#7A7E83", "selectedColor": "#10d21d", "borderStyle": "rgba(255,255,255,0.4)", "backgroundColor": "#fff", "list": [{"pagePath": "pages/home/<USER>", "iconPath": "static/home.png", "selectedIconPath": "static/icon/home_on.png", "text": "首页"}, {"pagePath": "pages/seckill/list", "iconPath": "static/seckill.png", "selectedIconPath": "static/icon/seckil_on.png", "text": "抢购"}, {"pagePath": "pages/order/orderList", "iconPath": "static/warehouse.png", "selectedIconPath": "static/icon/warehouse_on.png", "text": "仓库"}, {"pagePath": "pages/myPage/myPage", "iconPath": "static/my.png", "selectedIconPath": "static/icon/my_on.png", "text": "我的"}], "height": "50px"}, "launch_path": "__uniappview.html"}}