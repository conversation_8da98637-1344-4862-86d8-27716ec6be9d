var isReady = false;
var onReadyCallbacks = [];
var isServiceReady = false;
var onServiceReadyCallbacks = [];
var __uniConfig = {
  pages: [
    "pages/login/login",
    "pages/home/<USER>",
    "pages/seckill/index",
    "pages/seckill/list",
    "pages/warehouse/warehouse",
    "pages/myPage/myPage",
    "pages/myPage/sec_page/pwd",
    "pages/myPage/sec_page/pwd/fixpwd",
    "pages/myPage/sec_page/pwd/fixrealpwd",
    "pages/myPage/sec_page/todaymoney",
    "pages/myPage/sec_page/yestodaybefore",
    "pages/myPage/sec_page/teamorder",
    "pages/myPage/sec_page/signcenter",
    "pages/myPage/sec_page/getpay",
    "pages/myPage/sec_page/confirm",
    "pages/myPage/sec_page/myshare",
    "pages/myPage/sec_page/sharecode",
    "pages/myPage/sec_page/realname",
    "pages/myPage/sec_page/distribution",
    "pages/myPage/sec_page/merchant",
    "pages/order/orderList",
    "pages/order/todaygetpay",
    "pages/order/orderDetail",
    "pages/order/payDetail",
    "pages/project-detail/project-detail",
    "pages/transactiondetails/transactiondetails",
    "pages/myPage/sec_page/mygetmoney",
    "pages/terms/terms",
    "pages/privacy/privacy",
    "pages/login/register",
    "pages/login/register_2",
    "pages/address/address",
  ],
  window: {
    navigationBarTextStyle: "white",
    navigationBarBackgroundColor: "#5C3317",
    backgroundColor: "#FFFFFF",
  },
  tabBar: {
    color: "#7A7E83",
    selectedColor: "#10d21d",
    borderStyle: "white",
    backgroundColor: "#fff",
    list: [
      {
        pagePath: "pages/home/<USER>",
        iconPath: "static/home.png",
        selectedIconPath: "static/icon/home_on.png",
        text: "首页",
      },
      {
        pagePath: "pages/seckill/list",
        iconPath: "static/seckill.png",
        selectedIconPath: "static/icon/seckil_on.png",
        text: "抢购",
      },
      {
        pagePath: "pages/order/orderList",
        iconPath: "static/warehouse.png",
        selectedIconPath: "static/icon/warehouse_on.png",
        text: "仓库",
      },
      {
        pagePath: "pages/myPage/myPage",
        iconPath: "static/my.png",
        selectedIconPath: "static/icon/my_on.png",
        text: "我的",
      },
    ],
  },
  darkmode: false,
  nvueCompiler: "uni-app",
  nvueStyleCompiler: "weex",
  renderer: "auto",
  splashscreen: { alwaysShowBeforeRender: true, autoclose: false },
  appname: "玉智慧",
  compilerVersion: "4.36",
  entryPagePath: "pages/login/login",
  networkTimeout: {
    request: 60000,
    connectSocket: 60000,
    uploadFile: 60000,
    downloadFile: 60000,
  },
};
var __uniRoutes = [
  {
    path: "/pages/login/login",
    meta: { isQuit: true },
    window: { navigationBarTitleText: "登录" },
  },
  {
    path: "/pages/home/<USER>",
    meta: { isQuit: true, isTabBar: true },
    window: { navigationBarTitleText: "玉智慧商城" },
  },
  {
    path: "/pages/seckill/index",
    meta: {},
    window: { navigationBarTitleText: "抢购详情", enablePullDownRefresh: true },
  },
  {
    path: "/pages/seckill/list",
    meta: { isQuit: true, isTabBar: true },
    window: { navigationBarTitleText: "抢购列表" },
  },
  {
    path: "/pages/warehouse/warehouse",
    meta: {},
    window: { navigationBarTitleText: "仓库" },
  },
  {
    path: "/pages/myPage/myPage",
    meta: { isQuit: true, isTabBar: true },
    window: { navigationBarTitleText: "我的" },
  },
  {
    path: "/pages/myPage/sec_page/pwd",
    meta: {},
    window: { navigationBarTitleText: "修改密码" },
  },
  {
    path: "/pages/myPage/sec_page/pwd/fixpwd",
    meta: {},
    window: { navigationBarTitleText: "修改密码" },
  },
  {
    path: "/pages/myPage/sec_page/pwd/fixrealpwd",
    meta: {},
    window: { navigationBarTitleText: "交易密码" },
  },
  {
    path: "/pages/myPage/sec_page/todaymoney",
    meta: {},
    window: { navigationBarTitleText: "今日收支" },
  },
  {
    path: "/pages/myPage/sec_page/yestodaybefore",
    meta: {},
    window: { navigationBarTitleText: "往日收支" },
  },
  {
    path: "/pages/myPage/sec_page/teamorder",
    meta: {},
    window: { navigationBarTitleText: "团队订单" },
  },
  {
    path: "/pages/myPage/sec_page/signcenter",
    meta: {},
    window: { navigationBarTitleText: "签约中心" },
  },
  {
    path: "/pages/myPage/sec_page/getpay",
    meta: {},
    window: { navigationStyle: "custom" },
  },
  {
    path: "/pages/myPage/sec_page/confirm",
    meta: {},
    window: { navigationBarTitleText: "交易密码" },
  },
  {
    path: "/pages/myPage/sec_page/myshare",
    meta: {},
    window: { navigationBarTitleText: "我的分享" },
  },
  {
    path: "/pages/myPage/sec_page/sharecode",
    meta: {},
    window: { navigationBarTitleText: "分享二维码" },
  },
  {
    path: "/pages/myPage/sec_page/realname",
    meta: {},
    window: { navigationBarTitleText: "实名认证" },
  },
  {
    path: "/pages/myPage/sec_page/distribution",
    meta: {},
    window: { navigationBarTitleText: "分销中心" },
  },
  {
    path: "/pages/myPage/sec_page/merchant",
    meta: {},
    window: { navigationBarTitleText: "商家入驻" },
  },
  {
    path: "/pages/order/orderList",
    meta: { isQuit: true, isTabBar: true },
    window: { navigationBarTitleText: "仓库" },
  },
  {
    path: "/pages/order/todaygetpay",
    meta: {},
    window: { navigationBarTitleText: "今日收支" },
  },
  {
    path: "/pages/order/orderDetail",
    meta: {},
    window: { navigationBarTitleText: "订单详情" },
  },
  {
    path: "/pages/order/payDetail",
    meta: {},
    window: { navigationBarTitleText: "订单详情" },
  },
  {
    path: "/pages/project-detail/project-detail",
    meta: {},
    window: { navigationBarTitleText: "商品详情" },
  },
  {
    path: "/pages/transactiondetails/transactiondetails",
    meta: {},
    window: { navigationBarTitleText: "交易详情" },
  },
  {
    path: "/pages/myPage/sec_page/mygetmoney",
    meta: {},
    window: { navigationBarTitleText: "我的收益" },
  },
  {
    path: "/pages/terms/terms",
    meta: {},
    window: { navigationBarTitleText: "" },
  },
  {
    path: "/pages/privacy/privacy",
    meta: {},
    window: { navigationBarTitleText: "" },
  },
  {
    path: "/pages/login/register",
    meta: {},
    window: { navigationBarTitleText: "注册" },
  },
  {
    path: "/pages/login/register_2",
    meta: {},
    window: { navigationBarTitleText: "注册" },
  },
  {
    path: "/pages/address/address",
    meta: {},
    window: { navigationBarTitleText: "我的地址" },
  },
];
__uniConfig.onReady = function (callback) {
  if (__uniConfig.ready) {
    callback();
  } else {
    onReadyCallbacks.push(callback);
  }
};
Object.defineProperty(__uniConfig, "ready", {
  get: function () {
    return isReady;
  },
  set: function (val) {
    isReady = val;
    if (!isReady) {
      return;
    }
    const callbacks = onReadyCallbacks.slice(0);
    onReadyCallbacks.length = 0;
    callbacks.forEach(function (callback) {
      callback();
    });
  },
});
__uniConfig.onServiceReady = function (callback) {
  if (__uniConfig.serviceReady) {
    callback();
  } else {
    onServiceReadyCallbacks.push(callback);
  }
};
Object.defineProperty(__uniConfig, "serviceReady", {
  get: function () {
    return isServiceReady;
  },
  set: function (val) {
    isServiceReady = val;
    if (!isServiceReady) {
      return;
    }
    const callbacks = onServiceReadyCallbacks.slice(0);
    onServiceReadyCallbacks.length = 0;
    callbacks.forEach(function (callback) {
      callback();
    });
  },
});
service.register("uni-app-config", {
  create(a, b, c) {
    if (!__uniConfig.viewport) {
      var d = b.weex.config.env.scale,
        e = b.weex.config.env.deviceWidth,
        f = Math.ceil(e / d);
      Object.assign(__uniConfig, {
        viewport: f,
        defaultFontSize: Math.round(f / 20),
      });
    }
    return {
      instance: {
        __uniConfig: __uniConfig,
        __uniRoutes: __uniRoutes,
        global: void 0,
        window: void 0,
        document: void 0,
        frames: void 0,
        self: void 0,
        location: void 0,
        navigator: void 0,
        localStorage: void 0,
        history: void 0,
        Caches: void 0,
        screen: void 0,
        alert: void 0,
        confirm: void 0,
        prompt: void 0,
        fetch: void 0,
        XMLHttpRequest: void 0,
        WebSocket: void 0,
        webkit: void 0,
        print: void 0,
      },
    };
  },
});
